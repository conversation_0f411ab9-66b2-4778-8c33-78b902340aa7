<template>
  <div style="padding: 5px">
    <customer-reg-list4-summary ref="customerRegList" @row-click="handleRegTableRowClick" v-show="showRegList" />

    <div v-show="!showRegList">
      <a-alert message="请在人员列表中选择人员！" type="warning" v-if="!currentReg?.id" />
      <template v-else>
        <div class="exam-info-single-row" v-if="currentReg?.id">
          <span class="info-value strong" style="font-size: 14px"
            >{{ currentReg.name || '' }} {{ currentReg.gender || '' }} {{ currentReg.age ? currentReg.age + currentReg.ageUnit : '' }}</span
          >
          <a-tag class="exam-no">{{ currentReg.examNo ? currentReg.examNo : '' }}</a-tag>

          <span class="info-divider">|</span>

          <span class="info-label">状态:</span>
          <span class="info-value strong">{{ customerSummary.status || '未总检' }}</span>
          <a-tag v-if="customerSummary.initailDoctor" color="cyan" size="small"> 初:{{ customerSummary.initailDoctor }} </a-tag>
          <a-tag v-if="customerSummary.chiefDoctor" color="blue" size="small">主:{{ customerSummary.chiefDoctor }} </a-tag>
          <a-tag v-if="customerSummary.auditor" color="green" size="small">审:{{ customerSummary.auditor }}</a-tag>

          <span class="info-divider">|</span>

          <span class="info-label">类别:</span>
          <span class="info-value">{{ currentReg.examCategory || '' }}</span>

          <span class="info-divider">|</span>

          <span class="info-label">危害因素:</span>
          <span class="info-value">{{ currentReg.riskFactor ? currentReg.riskFactor_dictText : '' }}</span>

          <span class="info-divider">|</span>

          <span class="info-label">单位:</span>
          <span class="info-value">{{ currentReg.companyName ? currentReg.companyName : '' }}</span>

          <a-button type="link" class="return-list-icon" @click="returnToList" title="列表">
            <ArrowLeftOutlined />
            列表
          </a-button>
        </div>
      </template>
      <splitpanes class="default-theme">
        <pane size="25" style="padding: 2px">
          <a-card size="small">
            <template #extra>
              <a-space>
                <a-button @click="previewAllPic">全部图片</a-button>
                <a-button @click="openDataModal">历史数据</a-button>
                <a-spin :spinning="btnLoading">
                  <a-dropdown>
                    <template #overlay>
                      <a-menu @click="handleMenuClick">
                        <a-menu-item key="fixLisData"> 更新检验数据</a-menu-item>
                        <a-menu-item key="fixCheckData"> 更新检查数据</a-menu-item>
                        <a-menu-item key="navLis"> 检验系统</a-menu-item>
                        <a-menu-item key="navCheck"> 检查系统</a-menu-item>
                        <a-menu-item key="occuHistory"> 职业史</a-menu-item>
                        <a-menu-item key="printApply"> 打印导引单</a-menu-item>
                        <a-menu-item key="printerSetup"> 打印机设置</a-menu-item>
                        <a-menu-item key="healthQuest"> 健康问卷</a-menu-item>
                      </a-menu>
                    </template>

                    <a-button size="small">
                      更多
                      <DownOutlined />
                    </a-button>
                  </a-dropdown>
                </a-spin>
              </a-space>
            </template>
            <div style="padding: 0; height: 76vh; overflow-y: auto">
              <customer-reg-item-group-status ref="itemGroupStatus" @load-data="handleItemGroupStatus" />
            </div>
          </a-card>
        </pane>
        <pane size="75">
          <a-alert v-if="criticalItems.length > 0" :message="criticalTip" type="error" show-icon style="margin-bottom: 6px" />
          <a-alert
            v-if="showAuditTip"
            :message="'总检驳回：' + auditRecord.rejectReason"
            type="error"
            show-icon
            style="margin-bottom: 6px"
            :closable="true"
          />
          <a-tabs
            tabPosition="right"
            class="vertical-tab-text"
            :tabBarStyle="{ width: '14px' }"
            v-model:activeKey="summaryPannelKey"
            @change="handleSummaryPannelTab"
          >
            <a-tab-pane key="health">
              <template #tab>
                <div class="vertical-text">汇总和建议</div>
              </template>

              <a-card size="small" :bordered="false">
                <template #extra></template>
                <a-row :gutter="4">
                  <a-col :span="12">
                    <!-- 只读模式：使用专门的只读组件 -->
                    <AbnormalSummaryReadonly
                      v-if="isReadOnlyMode"
                      :modelValue="abnormalSummaryList"
                      :nurseAnnotations="nurseAnnotations"
                      :showAnnotations="showAnnotations"
                    />
                    <!-- 编辑模式：使用原有的编辑组件 -->
                    <AbnormalSummaryEditor3
                      v-else
                      ref="abnormalSummaryEditorRef"
                      @add-advice="handleAddAdvice"
                      @get-abnormal-summary="getAbnormalSummary"
                      :loading="departSummaryLoading"
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-tabs v-model:activeKey="summaryTabKey" size="small" style="width: 100%" @change="handleSummaryTabChange">
                      <template #rightExtra>
                        <a-space v-if="!isReadOnlyMode">
                          <a-button
                            size="small"
                            type="default"
                            @click="handleGetKnowledgeBaseSummary"
                            class="knowledge-base-button"
                            :loading="knowledgeBaseSummaryLoading"
                            v-if="summaryPannelKey == 'health' && summaryTabKey == 'advice'"
                          >
                            <template v-if="!knowledgeBaseSummaryLoading"> 知识库建议</template>
                            <template v-else> 生成中...</template>
                          </a-button>
                          <a-button
                            size="small"
                            type="primary"
                            @click="handleGetAiSummary"
                            class="ai-button"
                            :loading="aiSummaryLoading"
                            v-if="summaryPannelKey == 'health' && summaryTabKey == 'advice'"
                          >
                            <template v-if="!aiSummaryLoading"> AI建议</template>
                            <template v-else> 生成中...</template>
                          </a-button>
                        </a-space>
                      </template>
                      <a-tab-pane key="diagnosis" tab="诊断" :forceRender="true">
                        <div style="height: 70vh; overflow-y: auto">
                          <!-- 只读模式：使用专门的只读组件 -->
                          <DiagnosisReadonly v-if="isReadOnlyMode" :modelValue="diagnosisList" />
                          <!-- 编辑模式：使用原有的编辑组件 -->
                          <DiagnosisEditor v-else v-model="diagnosisList" @change="handleDiagnosChange" />
                        </div>
                      </a-tab-pane>

                      <a-tab-pane key="advice" tab="建议" :forceRender="true">
                        <!-- 只读模式：使用专门的只读组件 -->
                        <AdviceReadonly v-if="isReadOnlyMode" :modelValue="adviceList" />
                        <!-- 编辑模式：使用原有的编辑组件 -->
                        <AdviceEditor3
                          v-else
                          ref="adviceEditorRef"
                          :currentReg="currentReg"
                          @get-ai-summary="handleGetAiSummary"
                          @ai-loading-change="handleAiLoadingChange"
                        />
                      </a-tab-pane>
                    </a-tabs>
                  </a-col>
                </a-row>
              </a-card>
            </a-tab-pane>
            <a-tab-pane key="occu">
              <template #tab>
                <div class="vertical-text">职业检</div>
              </template>
              <a-card size="small">
                <ZyConclusionDetailList ref="zyConclusionDetailList" />
              </a-card>
            </a-tab-pane>
            <a-tab-pane key="healthCard">
              <template #tab>
                <div class="vertical-text">健康证</div>
              </template>
              <a-card size="small">
                <template #extra>
                  <a-space>
                    <a-button type="primary" @click="saveHealthCardSummary">保存</a-button>
                  </a-space>
                </template>
                <div style="height: 70vh; overflow-y: auto">
                  <a-form
                    ref="formRef"
                    class="antd-modal-form"
                    :labelCol="{ xs: { span: 24 }, sm: { span: 5 } }"
                    :wrapperCol="{ xs: { span: 24 }, sm: { span: 19 } }"
                  >
                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="结果">
                          <a-radio-group v-model:value="customerSummary.healthCardResult">
                            <a-radio-button value="合格">合格</a-radio-button>
                            <a-radio-button value="不合格">不合格</a-radio-button>
                          </a-radio-group>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </div>
              </a-card>
            </a-tab-pane>
          </a-tabs>
          <div style="height: 40px"></div>
          <div class="bottom-action-bar" v-if="summaryPannelKey === 'health' || summaryPannelKey === 'occu'">
            <div class="action-bar-content">
              <div class="left-actions"></div>

              <div class="right-actions">
                <a-button
                  type="primary"
                  @click="saveOrUpdateSummary('pre')"
                  :disabled="customerSummary.status == '审核通过'"
                  v-if="hasPermission('summary:presave')"
                >
                  <SaveOutlined />
                  保存初检
                </a-button>
                <a-button
                  type="primary"
                  :disabled="customerSummary.status == '审核通过'"
                  @click="saveOrUpdateSummary('normal')"
                  v-if="hasPermission('summary:customer_reg_summary:add')"
                  style="margin-left: 8px"
                >
                  <SaveOutlined />
                  保存主检
                </a-button>
                <a-button type="primary" @click="openAuditModal" v-if="hasPermission('summary:summary_audit_record:add')" style="margin-left: 8px">
                  <AuditOutlined />
                  审核
                </a-button>
                <a-button
                  type="primary"
                  @click="revokeAudit"
                  v-if="
                    (currentReg.summaryStatus == '审核通过' || currentReg.summaryStatus == '驳回') &&
                    hasPermission('summary:summary_audit_record:add')
                  "
                  style="margin-left: 8px"
                >
                  <ClearOutlined />
                  撤销
                </a-button>
                <a-button type="dashed" @click="openAuditRecordModal" style="margin-left: 8px">
                  <HistoryOutlined />
                </a-button>
                <a-divider type="vertical" />
                <a-spin :spinning="reportDataLoading">
                  <a-radio-group>
                    <a-radio-button value="large" @click="previewReport">预览</a-radio-button>
                    <a-radio-button value="default" @click="printReport">
                      <PrinterOutlined />
                      打印
                    </a-radio-button>
                  </a-radio-group>
                  <a-select
                    placeholder="请选择报告模版"
                    v-model:value="currentReportTemplateId"
                    style="width: 120px; margin-left: 8px"
                    :options="reportTemplateList"
                  />
                </a-spin>
              </div>
            </div>
          </div>

          <a-float-button
            shape="square"
            description="返回列表"
            type="default"
            :style="{
              right: '4px',
              bottom: '165px',
            }"
            @click="returnToList"
          >
            <template #icon>
              <ArrowLeftOutlined />
            </template>
          </a-float-button>

          <a-float-button
            shape="square"
            description="科室提醒"
            type="primary"
            :style="{
              right: '4px',
              bottom: '95px',
            }"
            :badge="{ count: departmentTipCount, showZero: false }"
            @click="openDepartTipDrawer"
          >
            <template #icon>
              <AlertOutlined />
            </template>
          </a-float-button>
        </pane>
      </splitpanes>
    </div>

    <!--    <Report ref="reportRef" />-->
    <upload-manage-modal :preview-file-list="fileList" title="图片管理" ref="registerUploadModal" @ok="handlePicChange" />
    <customer-reg-depart-tip-drawer
      ref="departTipRef"
      :regId="currentReg?.id"
      :departmentId="currentDepartments"
      @loaded="handleDepartTipDrawerLoaded"
    />
    <ComprehensiveInquiryQuestionnaire ref="inquiryModal" :customer-reg="currentReg" />
    <jimu-report-modal ref="jimuReportModal" title="历史结果" width="80%" :report-id="historyResultReportId" />
    <summary-audit-record-modal ref="summaryAuditRecordModal" @success="handleAuditOk" />
    <summary-audit-record-list-modal ref="summaryAuditRecordListModal" />
    <recheck-notify-pannel-modal ref="recheckNotifyPannelModal" @cancel="countRecheck" />
    <editor-modal ref="editorModal" @ok="setDepartSummaryText" />
    <PrinterSetupModal ref="printerSetupModalRef" />
    <Report ref="reportRef" />
    <HealthQuestAddModal ref="healthQuestAddModal" @ok="handleQuestOk" />
  </div>
</template>
<script lang="ts" setup name="SummaryPannel">
  import { computed, defineAsyncComponent, nextTick, onBeforeUnmount, onMounted, provide, reactive, ref, unref } from 'vue';
  import { CustomerRegCriticalItem, CustomerRegSummary, ICustomerReg, ICustomerRegItemResult } from '#/types';
  import { fetchCheckData, fetchLisData, getCriticalItem, saveItemResult } from '@/views/station/Station.api';
  import {
    getSummaryAdviceByText,
    listAbnormalSummaryByRegV2,
    querySummaryByRegId,
    revokeSummaryStatus,
    saveHealthCardResult,
    saveSummary,
    updateReportPrintTimes,
  } from '@/views/summary/Summary.api';
  import { list as listAditRecord } from './SummaryAuditRecord.api';
  import { Form, message, SelectProps, theme } from 'ant-design-vue';
  import UploadManageModal from '@/components/Upload/src/UploadManageModal.vue';
  import { preview } from 'vue3-image-preview';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import CustomerRegDepartTipDrawer from '@/views/station/CustomerRegDepartTipDrawer.vue';
  import {
    AlertOutlined,
    ArrowLeftOutlined,
    AuditOutlined,
    ClearOutlined,
    DownOutlined,
    HistoryOutlined,
    PrinterOutlined,
    SaveOutlined,
  } from '@ant-design/icons-vue';

  import ZyInquiryModal from '@/views/occu/components/ZyInquiryModal.vue';
  import JimuReportModal from '@/components/Report/JimuReportModal.vue';
  import CustomerRegList4Summary from '@/views/summary/CustomerRegList4Summary.vue';
  import _ from 'lodash';
  import SummaryAuditRecordModal from '@/views/summary/components/SummaryAuditRecordModal.vue';
  import SummaryAuditRecordListModal from '@/views/summary/components/SummaryAuditRecordListModal.vue';
  import RecheckNotifyPannelModal from '@/views/recheck/RecheckNotifyPannelModal.vue';
  import { countByCustomerRegId } from '@/views/recheck/RecheckNotify.api';
  import ZyConclusionDetailList from '@/views/summary/ZyConclusionDetailList.vue';
  import { getReportData } from '@/views/summary/CustomerRegSummary.api';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { getDefaultIdOfType, getReportTemplate, getTemplateById } from '@/views/basicinfo/Template.api';
  import { PrinterType, printReportDirect } from '@/utils/print';
  import EditorModal from '@/views/summary/EditorModal.vue';
  import PrinterSetupModal from '@/views/reg/PrinterSetupModal.vue';
  import { getGuidanceSheet, updateGuidancePrintTimes, updateHealthQuestId } from '@/views/reg/CustomerReg.api';
  import { usePermission } from '/@/hooks/web/usePermission';
  import CustomerRegItemGroupStatus from '@/views/summary/components/CustomerRegItemGroupStatus.vue';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import Report from '@/components/Report/Report.vue';
  import HealthQuestAddModal from '@/views/quest/components/HealthQuestAddModal.vue';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import DiagnosisEditor from '@/components/DiagnosisEditor/index.vue';
  import DiagnosisReadonly from '@/components/DiagnosisReadonly/index.vue';
  import AbnormalSummaryEditor3 from '@/views/summary/components/AbnormalSummaryEditor3.vue';
  import AbnormalSummaryReadonly from '@/views/summary/components/AbnormalSummaryReadonly.vue';
  import AdviceEditor3 from '@/views/summary/components/AdviceEditor3.vue';
  import AdviceReadonly from '@/views/summary/components/AdviceReadonly.vue';
  const ComprehensiveInquiryQuestionnaire = defineAsyncComponent(() => import('@/views/occu/components/ComprehensiveInquiryQuestionnaire.vue'));

  const userStore = useUserStore();
  const user = userStore.getUserInfo;

  const showRegList = ref(true);

  const { hasPermission } = usePermission();
  const { token } = theme.useToken();

  const { createConfirm, createErrorModal } = useMessage();
  const { useToken } = theme;

  const editorModal = ref();
  const summaryPannelKey = ref('');
  const summaryTabKey = ref('diagnosis');
  const healthQuestAddModal = ref(null);

  async function handleSummaryPannelTab(key) {
    summaryPannelKey.value = key;
  }

  function handleSummaryTabChange(key) {
    summaryTabKey.value = key;
  }

  const btnLoading = ref(false);

  // AI建议生成loading状态
  const aiSummaryLoading = ref(false);

  // 知识库建议生成loading状态
  const knowledgeBaseSummaryLoading = ref(false);

  // 计算只读模式
  const isReadOnlyMode = computed(() => {
    // 当总检状态为"审核通过"时，设置为只读
    return customerSummary.value?.status === '审核通过' || customerSummary.value?.lockFlag === '1';
  });

  /**体检人员列表部分*/
  const currentDepartments = ref('');
  const customerRegList = ref(null);
  const currentReg = ref<ICustomerReg>(null);

  const customerReg2Provide = ref();
  provide('customerReg4Summary', customerReg2Provide);
  /**科室汇总部分*/
  const originDepartGroupList = ref([]);
  const departSummaryLoading = ref(false);
  const departSummary = ref('');
  const abnormalSummaries = ref<any[]>([]);
  const abnormalSummaryEditorRef = ref<any>(null);
  const adviceEditorRef = ref<any>(null);

  //诊断列表
  const diagnosisList = ref<string[]>([]);

  // 异常汇总列表（用于只读组件）
  const abnormalSummaryList = computed(() => {
    return abnormalSummaries.value || [];
  });

  // 建议列表（用于只读组件）
  const adviceList = computed(() => {
    // 从customerSummary中获取建议数据
    if (customerSummary.value && customerSummary.value.summaryStructJson) {
      return customerSummary.value.summaryStructJson;
    } else if (customerSummary.value && customerSummary.value.summaryJson) {
      return customerSummary.value.summaryJson;
    }
    return [];
  });

  // 护士标注数据（用于只读组件）
  const nurseAnnotations = ref([]);

  // 是否显示标注（用于只读组件）
  const showAnnotations = ref(false);

  function handleDiagnosChange(list) {
    //diagnosisList.value = list;
  }

  function setDepartSummaryText(text) {
    departSummary.value = text;
  }

  function handleAuditOk(data) {
    if (data) {
      customerRegList.value?.reloadCurrent();
    }
  }

  function getAbnormalSummary() {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }
    departSummaryLoading.value = true;
    // 简化逻辑，恢复正常显示
    listAbnormalSummaryByRegV2({ customerRegId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          console.log('获取异常汇总数据响应:', res);

          // 注意：这里只在没有已保存的结构化数据时更新异常汇总数据
          // 如果当前没有保存的异常汇总数据，则使用新获取的数据
          abnormalSummaries.value = res.result;
          // 尝试更新编辑器内容
          if (abnormalSummaryEditorRef.value) {
            nextTick(() => {
              try {
                if (abnormalSummaries.value.length > 0) {
                  // 有数据时加载数据
                  abnormalSummaryEditorRef.value.loadAbnormalSummaries(abnormalSummaries.value);
                } else {
                  // 无数据时清空编辑器
                  abnormalSummaryEditorRef.value.clearContent();
                }
              } catch (error) {
                console.error('更新编辑器内容时发生错误:', error);
              }
            });
          }
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        departSummaryLoading.value = false;
      });
  }

  const handleAddAdvice = (advice) => {
    console.log('异常汇总编辑器，从候选列表添加建议:', advice);
    summaryTabKey.value = 'advice';
    nextTick(() => {
      // 触发AdviceEditor的addAdviceFromAbnormal方法
      if (adviceEditorRef.value) {
        adviceEditorRef.value.addAdviceFromAbnormal(advice);
      }
    });
  };

  // 处理AI建议生成请求
  const handleGetAiSummary = async () => {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }

    try {
      // 检查AdviceEditor3中是否已有建议内容
      if (adviceEditorRef.value && adviceEditorRef.value.hasNonEmptyContent()) {
        // 如果有内容，需要用户确认是否覆盖
        const confirmed = await new Promise<boolean>((resolve) => {
          createConfirm({
            iconType: 'warning',
            title: '确认生成AI建议',
            content: '当前建议列表不为空，生成AI建议将覆盖现有内容，是否继续？',
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        });

        if (!confirmed) {
          return; // 用户取消，不执行AI生成
        }
      }

      // 设置主组件loading状态
      aiSummaryLoading.value = true;

      // 获取异常汇总编辑器的内容
      let abnormalContent = [];

      if (abnormalSummaryEditorRef.value) {
        // 获取结构化的异常汇总数据
        abnormalContent = await abnormalSummaryEditorRef.value.getStructuredContent();
        // 触发AdviceEditor的getAiSummary方法
        if (adviceEditorRef.value) {
          await adviceEditorRef.value.getAiSummary(abnormalContent);
        }
      }
    } catch (error) {
      console.error('处理AI建议生成请求失败:', error);
      message.error('生成AI建议失败，请重试');
      // 出错时重置loading状态
      aiSummaryLoading.value = false;
    }
  };

  // 处理AI建议生成状态变化
  const handleAiLoadingChange = (loading: boolean) => {
    aiSummaryLoading.value = loading;
  };

  // 处理知识库建议生成请求
  // 生成优化的汇总文本
  const generateOptimizedSummaryText = (item: any): string => {
    // 1. 优先使用小项结论字段（checkConclusion）拼接
    if (item.relatedItemResults && item.relatedItemResults.length > 0) {
      const checkConclusions = item.relatedItemResults
        .map((result) => result.checkConclusion)
        .filter((conclusion) => conclusion && conclusion.trim() !== '')
        .map((conclusion) => cleanTextContent(conclusion)); // 清理文本内容

      if (checkConclusions.length > 0) {
        return checkConclusions.join('\n');
      }
    }

    // 2. 降级使用text字段（手动添加的异常条目）
    if (item.text && item.text.trim() !== '') {
      return cleanTextContent(item.text);
    }

    // 3. 最后使用summaryText字段
    if (item.summaryText && item.summaryText.trim() !== '') {
      return cleanTextContent(item.summaryText);
    }

    return '';
  };

  // 清理文本内容，去掉中英文括号、方括号、大括号及其内容
  const cleanTextContent = (text: string): string => {
    if (!text) return '';

    return (
      text
        // 去掉中文括号及其内容：（内容）
        .replace(/（[^）]*）/g, '')
        // 去掉英文括号及其内容：(内容)
        .replace(/\([^)]*\)/g, '')
        // 去掉方括号及其内容：[内容]
        .replace(/\[[^\]]*\]/g, '')
        // 去掉大括号及其内容：{内容}
        .replace(/\{[^}]*\}/g, '')
        // 去掉多余的空格
        .replace(/\s+/g, ' ')
        // 去掉首尾空格
        .trim()
    );
  };

  const handleGetKnowledgeBaseSummary = async () => {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }

    try {
      // 检查AdviceEditor3中是否已有建议内容
      if (adviceEditorRef.value && adviceEditorRef.value.hasNonEmptyContent()) {
        // 如果有内容，需要用户确认是否覆盖
        const confirmed = await new Promise<boolean>((resolve) => {
          createConfirm({
            iconType: 'warning',
            title: '确认获取知识库建议',
            content: '当前建议列表不为空，获取知识库建议将覆盖现有内容，是否继续？',
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        });

        if (!confirmed) {
          return; // 用户取消，不执行知识库建议获取
        }
      }

      // 设置知识库建议loading状态
      knowledgeBaseSummaryLoading.value = true;

      // 获取异常汇总编辑器的内容
      let abnormalContent = [];
      let summaryText = '';

      if (abnormalSummaryEditorRef.value) {
        // 获取结构化的异常汇总数据
        abnormalContent = await abnormalSummaryEditorRef.value.getStructuredContent();
        // 优化异常汇总转换为文本格式的逻辑
        summaryText = abnormalContent
          .map((item) => {
            const optimizedText = generateOptimizedSummaryText(item);
            return optimizedText;
          })
          .filter((text) => text.trim() !== '')
          .join('\n');

        console.log('最终发送给知识库的汇总文本:', summaryText);
      }

      // 调用知识库建议接口
      const requestData = {
        summaryText: summaryText,
        customerRegId: currentReg.value.id,
        aiSummary: false, // false表示获取知识库建议
        abnormalDepartSummary: true,
      };

      console.log('调用知识库建议接口，参数:', requestData);

      const response = await getSummaryAdviceByText(requestData);

      console.log('知识库建议接口响应:', response);

      // 将获取到的建议数据传递给AdviceEditor3
      if (adviceEditorRef.value && response) {
        // 检查响应数据结构
        let adviceArray = [];

        if (Array.isArray(response)) {
          // 如果直接是数组
          adviceArray = response;
        } else if (response.adviceList && Array.isArray(response.adviceList)) {
          // 如果是包装在adviceList中的数组
          adviceArray = response.adviceList;
        } else if (response.result && Array.isArray(response.result)) {
          // 如果是包装在result中的数组
          adviceArray = response.result;
        } else {
          console.warn('未识别的响应数据结构:', response);
          message.warning('知识库建议数据格式异常');
          return;
        }

        console.log('解析后的建议数组:', adviceArray);

        // 直接使用原始数据，不做任何内容修改
        const validAdvices = adviceArray.filter((item) => item && (item.name?.trim() || item.content?.trim()));

        if (validAdvices.length === 0) {
          message.info('未获取到有效的知识库建议');
          return;
        }

        // 转换数据格式以适配AdviceEditor3，保持原始内容不变
        const adviceData = validAdvices.map((item, index) => ({
          seq: (index + 1).toString(),
          name: item.name || '',
          content: item.content || '',
          text: item.name && item.content ? `${item.name}：${item.content}` : item.name || item.content || '',
          chk: false,
          valid: !!item.content?.trim(),
          source: item.source || 'KnowledgeBase',
          key: Date.now().toString(36) + Math.random().toString(36).substr(2),
          diagnosticCriteria: item.diagnosticCriteria || '',
        }));

        console.log('转换后的建议数据:', adviceData);

        // 加载建议到编辑器
        adviceEditorRef.value.loadAdvices(adviceData);
        message.success(`知识库建议获取成功，共${adviceData.length}条建议`);
      } else {
        message.info('未获取到知识库建议');
      }
    } catch (error) {
      console.error('获取知识库建议失败:', error);
      message.error('获取知识库建议失败，请重试');
    } finally {
      // 重置loading状态
      knowledgeBaseSummaryLoading.value = false;
    }
  };

  /***健康证*/
  const healthCardForm = reactive<Record<string, any>>({
    healthCardResult: '合格',
  });
  const useForm = Form.useForm;

  async function saveHealthCardSummary() {
    if (customerSummary.value?.healthCardResult == null) {
      message.error('请选择健康证结果');
      return;
    }
    let data = {
      customerRegId: currentReg.value.id,
      summaryId: customerSummary.value.id,
      healthCardResult: customerSummary.value.healthCardResult,
    };
    await saveHealthCardResult(data);
  }

  /**项目组合部分*/
  const itemGroupStatus = ref();
  const departGroupList = ref([]);

  function handleItemGroupStatus(data) {
    departGroupList.value = data;
    originDepartGroupList.value = data;
  }

  /**复查项目部分*/
  const recheckTotal = ref(0);
  const recheckNotifyPannelModal = ref();

  function countRecheck() {
    if (!currentReg.value?.id) {
      return;
    }
    countByCustomerRegId({ customerRegId: currentReg.value.id }).then((res) => {
      recheckTotal.value = res;
    });
  }

  function openRecheckNotifyPannel() {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }
    if (!customerSummary.value?.id) {
      message.error('请先保存总检结果');
      return;
    }
    recheckNotifyPannelModal.value?.open(currentReg.value.id, customerSummary.value.id);
  }

  async function handleQuestOk(personalQuestId) {
    await updateHealthQuestId({ id: currentReg.value.id, personalQuestId: personalQuestId });
    customerRegList.value?.reloadCurrent();
  }

  /**总检建议*/
  const aiSummary = ref(true);
  const historyResultReportId = ref();
  const abnormalDepartSummary = ref(true);
  const customerSummary = ref<CustomerRegSummary>({});
  provide('customerSummary', customerSummary);
  const adviceKeywords = ref('');

  const summaryAdviceLoading = ref(false);
  const adviceSpinText = ref('正在获取建议');

  function getSettingFromLocalStorage(key: string, defaultValue: boolean): boolean {
    const value = localStorage.getItem(key);
    return value !== null ? JSON.parse(value) : defaultValue;
  }

  function confirmReplaceAdvice() {
    return new Promise<void>((resolve, reject) => {
      // 检查建议编辑器是否有内容
      if (adviceEditorRef.value && adviceEditorRef.value.hasNonEmptyContent && adviceEditorRef.value.hasNonEmptyContent()) {
        createConfirm({
          iconType: 'warning',
          title: '获取自动建议提示',
          content: '当前建议列表不为空，是否覆盖？',
          onOk: () => {
            resolve();
          },
          onCancel: () => {
            reject();
          },
        });
      } else {
        resolve();
      }
    });
  }

  function adoptAdvice(advice) {
    console.log('SummaryPannel: Adopting advice from knowledge base:', advice);
    // 使用AdviceEditor3的addAdviceFromKnowledgeBase方法
    if (adviceEditorRef.value) {
      // 如果当前不在建议面板，切换到建议面板
      if (summaryTabKey.value !== 'advice') {
        summaryTabKey.value = 'advice';
      }

      nextTick(() => {
        adviceEditorRef.value.addAdviceFromKnowledgeBase(advice);
      });
    } else {
      message.warn('建议编辑器未加载，请稍后重试');
    }
  }

  let handleEditAdviceName = _.debounce((e) => {
    adviceKeywords.value = e.target.value;
  }, 100);

  function handleMouseUp(event) {
    const textArea = event.target;
    const start = textArea.selectionStart;
    const end = textArea.selectionEnd;
    adviceKeywords.value = textArea.value.substring(start, end);
  }

  function checkAllMajorItemsSummarized(): string[] {
    return originDepartGroupList.value.filter((departGroup) => !departGroup.summaryFlag).map((departGroup) => departGroup.depart.departName);
  }

  // 检测是否包含AI生成的建议
  const checkForAiAdvice = async (): Promise<boolean> => {
    if (!adviceEditorRef.value) {
      return false;
    }

    try {
      const adviceData = await adviceEditorRef.value.getStructuredContent();
      const hasAi = adviceData.some((item) => item.source === 'AI');
      console.log('AI建议检测结果:', {
        总建议数: adviceData.length,
        建议列表: adviceData.map((item) => ({ name: item.name, source: item.source })),
        包含AI建议: hasAi,
      });
      return hasAi;
    } catch (error) {
      console.error('检测AI建议时发生错误:', error);
      return false;
    }
  };

  // 显示AI建议免责说明弹窗
  const showAiDisclaimerModal = (): Promise<boolean> => {
    return new Promise((resolve) => {
      createConfirm({
        iconType: 'warning',
        title: 'AI建议免责说明',
        content: `
          <div style="text-align: left; line-height: 1.6;">
            <p><strong>重要提示：</strong></p>
            <p>检测到您使用了AI生成的总检建议，请注意以下事项：</p>
            <ul style="margin: 10px 0; padding-left: 20px;">
              <li>AI建议仅供参考，不能替代医生的专业判断</li>
              <li>请务必根据患者实际情况和临床经验进行核实和调整</li>
              <li>AI建议可能存在不准确或不完整的情况</li>
              <li>最终的诊断和建议应由具备相应资质的医务人员确认</li>
              <li>使用AI建议的责任由操作医生承担</li>
            </ul>
            <p><strong>确认继续保存表示您已知晓并同意承担相应责任。</strong></p>
          </div>
        `,
        width: 500,
        onOk: () => {
          resolve(true);
        },
        onCancel: () => {
          resolve(false);
        },
      });
    });
  };

  async function saveOrUpdateSummary(type: string = '') {
    //校验
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (type == 'normal' && !customerSummary.value.preAuditor) {
      message.warn('请先保存初检！');
      return;
    }
    /* if (departSummary.value == '') {
message.warn('请填写汇总');
return;
}*/
    /* if (adviceList.value.length == 0) {
message.warn('请填写建议');
return;
}*/

    // 检测是否包含AI建议
    const hasAiAdvice = await checkForAiAdvice();
    if (hasAiAdvice) {
      const confirmed = await showAiDisclaimerModal();
      if (!confirmed) {
        return; // 用户取消保存
      }
    }

    const unsummarizedItems = checkAllMajorItemsSummarized();
    if (unsummarizedItems.length > 0) {
      createConfirm({
        iconType: 'warning',
        title: '确认总检提示',
        content: `以下科室未填写小结：${unsummarizedItems.join(', ')}，是否继续保存？`,
        onOk: async () => {
          await performSave(type);
        },
      });
    } else {
      await performSave(type);
    }
  }

  async function performSave(type) {
    try {
      // 1. 从异常汇总编辑器获取结构化数据
      let abnormalSummaryData = [];
      if (abnormalSummaryEditorRef.value) {
        abnormalSummaryData = await abnormalSummaryEditorRef.value.getStructuredContent();
      }

      // 2. 从诊断编辑器获取结构化数据
      let diagnosisData = diagnosisList.value || [];

      // 3. 从总检建议编辑器获取结构化数据
      let adviceData = [];
      if (adviceEditorRef.value) {
        adviceData = await adviceEditorRef.value.getStructuredContent();
      }

      // 4. 整理为兼容老版总检的数据结构
      let summaryJson = adviceData.map((item, index) => ({
        seq: index + 1,
        name: item.name || '',
        content: item.content || '',
        source: item.source || 'editor',
        markdown: true, // 标记内容为 markdown 格式
      }));

      // 生成传统的建议文本，保持向后兼容
      let advice = adviceData
        .map((item) => {
          const content = item.content || '';
          // 移除 markdown 标记，提取纯文本内容
          const plainText = content
            .replace(/\*\*(.*?)\*\*/g, '$1') // 移除加粗标记
            .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
            .replace(/^# (.*?)$/gm, '$1') // 移除 H1
            .replace(/^## (.*?)$/gm, '$1') // 移除 H2
            .replace(/^### (.*?)$/gm, '$1'); // 移除 H3

          return plainText;
        })
        .join('\r\n');

      // 5. 将诊断列表用换行符拼接成diagnosisSummary字段
      let diagnosisSummary = diagnosisData
        .filter((diagnosis) => diagnosis && diagnosis.trim() !== '') // 过滤空诊断
        .join('\n'); // 用换行符拼接

      // 6. 将异常汇总数据转换为文本格式，保存到characterSummary字段（向后兼容）
      let characterSummary = abnormalSummaryData
        .map((item) => item.text || item.summaryText || '') // 提取汇总文本
        .filter((text) => text.trim() !== '') // 过滤空文本
        .join('\n'); // 用换行符拼接

      // 7. 构建保存数据，同时保存结构化数据和兼容格式
      let postData = {
        id: customerSummary.value.id,
        characterSummary: characterSummary, // 异常汇总的文本格式（老版本兼容）
        summaryJson: summaryJson,
        advice: advice,
        type: type,
        customerRegId: currentReg.value.id,
        examNo: currentReg.value.examNo,
        // 新增结构化数据字段
        abnormalSummaryData: abnormalSummaryData,
        diagnosisList: diagnosisData,
        adviceStructuredData: adviceData,
        // 新增诊断字段，用换行符拼接
        diagnosisSummary: diagnosisSummary,
      };

      console.log('保存数据:', {
        abnormalSummaryCount: abnormalSummaryData.length,
        diagnosisCount: diagnosisData.length,
        adviceCount: adviceData.length,
        characterSummary: characterSummary,
        diagnosisSummary: diagnosisSummary,
        postData,
      });

      await saveSummary(postData);
      message.success('保存成功');
      customerRegList.value?.reloadCurrent();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请重试');
    }
  }

  const summaryAuditRecordModal = ref(null);
  const summaryAuditRecordListModal = ref(null);
  const auditRecordList = ref([]);
  const showAuditTip = ref(false);
  const auditRecord = ref(null);

  function getAditRecord(summaryId) {
    listAditRecord({ summaryId: summaryId }).then((res) => {
      auditRecordList.value = res.records;
      if (auditRecordList.value.length > 0 && auditRecordList.value[0].auditResult == '驳回') {
        showAuditTip.value = true;
        auditRecord.value = auditRecordList.value[0];
      }
    });
  }

  function openAuditModal() {
    if (!customerSummary.value.id) {
      message.warn('请先保存总检信息！');
      return;
    }
    if (!currentReg.value.id) {
      message.warn('请选择体检人员！');
      return;
    }
    if (!customerSummary.value.preAuditor) {
      message.warn('尚未初检，不能审核！');
      return;
    }
    if (!customerSummary.value.creatorName) {
      message.warn('尚未主检，不能审核！');
      return;
    }
    summaryAuditRecordModal.value?.add(customerSummary.value, true);
  }

  function openAuditRecordModal() {
    summaryAuditRecordListModal.value?.open(customerSummary.value.id);
  }

  function revokeAudit() {
    if (user.username !== customerSummary.value.auditeBy) {
      message.error('只有审核者本人才能撤销审核');
      return;
    }
    //检查是否已审核
    createConfirm({
      iconType: 'warning',
      title: '撤销审核提示',
      content: '确定要撤销审核吗？',
      onOk: () => {
        let data = {
          summaryId: customerSummary.value.id,
          customerRegId: currentReg.value.id,
        };
        revokeSummaryStatus(data).then((res) => {
          //customerSummary.value.status = res;
          //currentReg.value.summaryStatus = res;
          customerRegList.value?.reloadCurrent();
          //message.success('撤销审核成功！');
        });
      },
    });
  }

  /**科室结果图片*/
  const currentItemResult = ref<ICustomerRegItemResult>({});
  const priewPic = (urls) => {
    preview({
      images: urls.map((item) => getFileAccessHttpUrl(item)),
    });
  };

  const printerSetupModalRef = ref(null);

  function handleMenuClick(menu) {
    //console.log('handleMenuClick', menu);
    if (menu.key === 'allPic') {
      previewAllPic();
    } else if (menu.key === 'occuHistory') {
      openInquiry();
    } else if (menu.key === 'printerSetup') {
      openPrinterSetupModal();
    } else if (menu.key === 'printApply') {
      printCurrentGuide();
    } else if (menu.key === 'hisData') {
      openDataModal();
    } else if (menu.key === 'fixLisData') {
      fixLisData();
    } else if (menu.key === 'fixCheckData') {
      fixCheckData();
    } else if (menu.key === 'navLis') {
      navLis();
    } else if (menu.key === 'navCheck') {
      navPacs();
    } else if (menu.key === 'healthQuest') {
      healthQuest();
    }
  }

  /**打印导引单*/
  async function printCurrentGuide() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录');
      return;
    }
    printGuide(currentReg.value);
    //printGuide(currentReg.value);
  }

  function getGuideTemplate() {
    return new Promise((resolve, reject) => {
      getDefaultIdOfType({ type: '导引单' }).then((res) => {
        if (res.success) {
          let templateId = res.result;
          getTemplateById({ id: templateId }).then((templateRes) => {
            resolve(JSON.parse(templateRes.content));
          });
        }
      });
    });
  }

  async function printGuide(reg) {
    let regUnref = unref(reg);
    if (!regUnref) {
      return;
    }
    try {
      const customerRegDetail = await getGuidanceSheet({ id: regUnref.id });
      if (customerRegDetail.reg.avatar) {
        customerRegDetail.reg.avatar = getFileAccessHttpUrl(customerRegDetail.reg.avatar);
      }
      let template = await getGuideTemplate();
      if (!template) {
        message.error('未找到导引单模板');
        return;
      }
      template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(customerRegDetail);
      await printReportDirect(template, PrinterType.Guide);
      updateGuidancePrintTimes({ regId: regUnref.id });
    } catch (e) {
      console.log(e);
    }
  }

  function openPrinterSetupModal() {
    printerSetupModalRef.value?.open();
  }

  /**第三方接口url*/
  const lisUrl = ref('');
  const checkUrl = ref('');

  function fetchUrlSetting() {
    querySysParamByCode({ code: 'lis_url' }).then((res) => {
      lisUrl.value = res.result;
    });
    querySysParamByCode({ code: 'check_url' }).then((res) => {
      checkUrl.value = res.result;
    });
  }

  const navLis = () => {
    if (!currentReg.value.examNo) {
      message.error('请选择体检记录！');
      return;
    }
    if (!lisUrl.value) {
      message.error('未配置LIS系统地址！');
      return;
    }
    let regId = currentReg.value.id;
    // Replace {regId} in the URL with the actual regId
    const fullUrl = lisUrl.value.replace('{regId}', regId);
    window.open(fullUrl, '_blank');
  };

  const navPacs = () => {
    if (!currentReg.value.examNo) {
      message.error('请选择体检记录！');
      return;
    }
    if (!checkUrl.value) {
      message.error('未配置检查系统地址！');
      return;
    }
    let regId = currentReg.value.id;
    const fullUrl = checkUrl.value.replace('{regId}', regId);
    window.open(fullUrl, '_blank');
  };

  const previewAllPic2 = () => {
    const picSet = new Set<string>();
    departGroupList.value.forEach((depart) => {
      depart.groupList.forEach((group) => {
        if (group.reportPics && group.reportPics.length > 0) {
          group.reportPics.forEach((pic) => picSet.add(pic));
        }
      });
    });

    const uniquePicList = Array.from(picSet);
    if (uniquePicList.length > 0) {
      preview({
        images: uniquePicList.map((item) => getFileAccessHttpUrl(item)),
      });
    } else {
      message.info('没有图片!');
    }
  };
  const previewAllPic = () => {
    const picSet = new Set<string>();
    const reportPdfInterfaceSet = new Set<string>();
    departGroupList.value.forEach((depart) => {
      depart.groupList.forEach((group) => {
        if (!group.reportPdfInterface) {
          // 处理 reportPdfInterface 为 null 的情况
          if (group.reportPics && group.reportPics.length > 0) {
            group.reportPics.forEach((pic) => {
              picSet.add(pic);
            });
            // 注意：这里我们不需要再为 null 创建一个特殊的标记，
            // 因为我们通过检查 reportPdfInterface 的值来隐式地处理了这种情况。
          }
        } else if (!reportPdfInterfaceSet.has(group.reportPdfInterface) && group.reportPics && group.reportPics.length > 0) {
          // 处理非 null 和非空的 reportPdfInterface
          reportPdfInterfaceSet.add(group.reportPdfInterface);
          group.reportPics.forEach((pic) => picSet.add(pic));
        }
      });
    });
    const uniquePicList = Array.from(picSet);
    if (uniquePicList.length > 0) {
      preview({
        images: uniquePicList.map((item) => getFileAccessHttpUrl(item)),
      });
    } else {
      message.info('没有图片!');
    }
  };

  function healthQuest() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    healthQuestAddModal.value?.open(currentReg.value);
  }

  const registerUploadModal = ref(null);
  const fileList = ref<string[]>([]);

  function handlePicChange(urls: string[]) {
    //找出新增或删除的图片
    let addList = urls.filter((item) => !fileList.value.includes(item));
    let delList = fileList.value.filter((item) => !urls.includes(item));
    if (addList.length > 0 || delList.length > 0) {
      //更新对应项目的图片
      currentItemResult.value.pic = urls;
      saveItemResult({ resultList: [currentItemResult.value] });
    }
  }

  /**职业病问诊*/
  /**职业病问诊*/
  const inquiryModal = ref(null);

  async function openInquiry() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }

    // 确保DOM更新完成
    await nextTick();
    inquiryModal.value.open(currentReg.value, false);
  }

  const jimuReportModal = ref();

  function openDataModal() {
    jimuReportModal.value?.open({ archivesNum: currentReg.value.archivesNum });
  }

  function fixLisData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchLisData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检验数据更新成功！');
          customerRegList.value?.reloadCurrent();
        } else {
          message.error('检验数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  function fixCheckData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchCheckData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检查数据数据更新成功！');
          customerRegList.value?.reloadCurrent();
        } else {
          message.error('检查数据数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  /**科室提醒抽屉部分*/
  const departmentTipCount = ref(0);
  const departTipRef = ref(null);

  function openDepartTipDrawer() {
    departTipRef.value?.open();
  }

  function handleDepartTipDrawerLoaded(count: number) {
    departmentTipCount.value = count;
  }

  /**危急值提示部分*/
  function showCriticalItems(criticalItems: CustomerRegCriticalItem[], refresh: boolean = false) {
    if (criticalItems.length == 0) {
      return;
    }
    // 根据severityDegree分类（A类和B类）
    const categoryA = criticalItems.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.filter((item) => item.severityDegree === 'B类');

    // 然后据此拼接提示信息
    const messageA = categoryA
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join(' <br/>');
    const messageB = categoryB
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join(' <br/>');

    let title = '';
    if (categoryA.length > 0) {
      title += `发现${categoryA.length}项A类危急值 `;
    }
    if (categoryB.length > 0) {
      title += `${categoryB.length}项B类危急值`;
    }

    let message = '<div style="max-height: 50vh;overflow-y: auto;">';
    if (categoryA.length > 0) {
      message += `<span style="color:#f5222d;font-weight: normal">A类（需要立即进行临床干预，否则
将危及生命或导致严重不良后果的异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageA}</p><br/>`;
    }
    if (categoryB.length > 0) {
      message += `<span style="color:#faad14;font-weight: bold;">B类（需要临床进一步检查以明确诊断和(或)需要
医学治疗疗的重要异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageB}</p>`;
    }
    message += '</div>';

    // 显示提示信息
    createErrorModal({
      title: title,
      content: message,
      iconType: 'warning',
      onOk: () => {
        if (refresh) {
          getCriticalItemByRegId();
        }
      },
    });
  }

  const criticalItems = ref<CustomerRegCriticalItem[]>([]);

  function getCriticalItemByRegId(showTip: boolean = false) {
    getCriticalItem({ regId: currentReg.value.id }).then((res) => {
      if (showTip) {
        showCriticalItems(res, false);
      }
      criticalItems.value = res;
    });
  }

  const criticalTip = computed(() => {
    let tip = '';

    if (criticalItems.value.length == 0) {
      return tip;
    }
    const categoryA = criticalItems.value.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.value.filter((item) => item.severityDegree === 'B类');

    const messageA = categoryA
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join('；');
    const messageB = categoryB
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join('；');

    if (categoryA.length > 0) {
      tip += `发现${categoryA.length}项A类危急值：${messageA}；`;
    }
    if (categoryB.length > 0) {
      tip += `发现${categoryB.length}项B类危急值：${messageB}；`;
    }

    return tip;
  });

  function handleRegTableRowClick(selectedRow) {
    // 清除之前的加载状态
    summaryAdviceLoading.value = false;
    departSummaryLoading.value = false;

    // 更新当前患者信息
    currentReg.value = selectedRow;
    customerReg2Provide.value = selectedRow;

    if (selectedRow.id) {
      //open.value = false;
      showRegList.value = false;
      if (currentReg.value.examCategory == '职业病体检') {
        summaryPannelKey.value = 'occu';
      } else if (currentReg.value.examCategory == '健康证体检') {
        summaryPannelKey.value = 'healthCard';
      } else {
        summaryPannelKey.value = 'health';
      }

      // 重置所有数据
      departSummary.value = '';
      diagnosisList.value = [];
      abnormalSummaries.value = [];
      customerSummary.value = {};

      // 清空编辑器内容
      if (abnormalSummaryEditorRef.value) {
        abnormalSummaryEditorRef.value.clearContent();
      }
      if (adviceEditorRef.value) {
        adviceEditorRef.value.clearContent();
      }

      // 获取新数据
      fetchData();
    }
  }

  function returnToList() {
    // 清空当前选中的记录
    currentReg.value = null;
    customerReg2Provide.value = null;

    // 显示列表
    showRegList.value = true;
    summaryPannelKey.value = 'health';
  }

  function fetchData() {
    auditRecordList.value = [];
    showAuditTip.value = false;
    auditRecord.value = null;

    if (currentReg.value.id) {
      getCriticalItemByRegId(true);
      //getAbnormalSummary();
      getSummaryData(); // 获取总检数据
      countRecheck();
      filterTemplateByReg();
      nextTick(() => {
        itemGroupStatus.value?.loadData(currentReg.value.id, 'abnormal');
      });
    }
  }

  // 获取总检数据
  function getSummaryData() {
    if (!currentReg.value?.id) {
      return;
    }

    querySummaryByRegId({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          customerSummary.value = res.result;

          // 1. 加载异常汇总数据 - 优先使用结构化数据
          if (res.result.abnormalSummaryData && res.result.abnormalSummaryData.length > 0) {
            // 使用新的结构化异常汇总数据
            abnormalSummaries.value = res.result.abnormalSummaryData;
            console.log('使用结构化异常汇总数据:', res.result.abnormalSummaryData);
          } else if (res.result.abnormalSummaryList && res.result.abnormalSummaryList.length > 0) {
            // 使用老版本的异常汇总数据
            abnormalSummaries.value = res.result.abnormalSummaryList;
            console.log('使用老版本异常汇总数据:', res.result.abnormalSummaryList);
          } else if (res.result.characterSummary && res.result.characterSummary.trim() !== '') {
            // 使用characterSummary字段，转换为简单的异常汇总格式
            const summaryLines = res.result.characterSummary.split('\n').filter((line) => line.trim() !== '');
            abnormalSummaries.value = summaryLines.map((line, index) => ({
              text: line.trim(),
              summaryText: line.trim(),
              title: `异常汇总${index + 1}`,
              format: '{{text}}',
              itemSummaryTextList: [line.trim()],
              relatedItemResults: [],
              relatedItemGroupList: [],
            }));
            console.log('使用characterSummary数据转换为异常汇总:', abnormalSummaries.value);
          } else {
            abnormalSummaries.value = [];
          }

          // 更新异常汇总编辑器
          if (abnormalSummaryEditorRef.value) {
            nextTick(() => {
              try {
                if (abnormalSummaries.value.length > 0) {
                  abnormalSummaryEditorRef.value.loadAbnormalSummaries(abnormalSummaries.value);
                } else {
                  abnormalSummaryEditorRef.value.clearContent();
                }
              } catch (error) {
                console.error('更新异常汇总编辑器时发生错误:', error);
              }
            });
          }

          // 2. 加载诊断数据 - 优先使用结构化数据
          if (res.result.diagnosisList && res.result.diagnosisList.length > 0) {
            // 使用结构化诊断数据
            diagnosisList.value = res.result.diagnosisList;
            console.log('使用结构化诊断数据:', res.result.diagnosisList);
          } else if (res.result.diagnosisSummary && res.result.diagnosisSummary.trim() !== '') {
            // 使用老版本的diagnosisSummary字段，用换行符分割
            diagnosisList.value = res.result.diagnosisSummary.split('\n').filter((item) => item.trim() !== '');
            console.log('使用老版本诊断数据:', res.result.diagnosisSummary);
          } else {
            diagnosisList.value = [];
          }

          // 3. 加载建议数据到AdviceEditor3 - 优先使用结构化数据
          if (adviceEditorRef.value) {
            nextTick(() => {
              try {
                if (res.result.adviceStructuredData && res.result.adviceStructuredData.length > 0) {
                  // 使用新的结构化建议数据
                  adviceEditorRef.value.loadAdvices(res.result.adviceStructuredData);
                  console.log('使用结构化建议数据:', res.result.adviceStructuredData);
                } else if (res.result.summaryStructJson && res.result.summaryStructJson.length > 0) {
                  // 使用summaryStructJson
                  adviceEditorRef.value.loadAdvices(res.result.summaryStructJson);
                  console.log('使用summaryStructJson数据:', res.result.summaryStructJson);
                } else if (res.result.summaryJson && res.result.summaryJson.length > 0) {
                  // 使用老版本的summaryJson
                  adviceEditorRef.value.loadAdvices(res.result.summaryJson);
                  console.log('使用老版本summaryJson数据:', res.result.summaryJson);
                } else {
                  adviceEditorRef.value.clearContent();
                }
              } catch (error) {
                console.error('加载建议数据时发生错误:', error);
              }
            });
          }

          if (res.result.id) {
            getAditRecord(customerSummary.value.id);
          }
        }
      })
      .catch((error) => {
        console.error('获取总检数据失败:', error);
      });
  }

  /**报告预览打印*/
  const reportRef = ref(null);
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const currentReportTemplateId = ref(null);
  const reportDataLoading = ref(false);

  async function previewReport() {
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    //获取报告模版内容
    reportDataLoading.value = true;
    try {
      const templateRes = await getTemplateById({ id: currentReportTemplateId.value });
      let template = JSON.parse(templateRes.content);
      const reportDataRes = await getReportData({ customerRegId: currentReg.value.id });
      if (reportDataRes.success) {
        let reportData = reportDataRes.result;
        //需要处理报告中的图片
        //console.log(reportData);
        if (reportData.reportImgList?.length > 0) {
          reportData.reportImgList?.forEach((item) => {
            item.text = getFileAccessHttpUrl(item.text);
          });
        }
        //console.log(reportData.groupByFunctionMap?.lab_exam);

        if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap) {
          Object.keys(reportData.groupByFunctionPicMap).forEach((key) => {
            reportData.groupByFunctionPicMap[key].forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          });
        }

        //console.log(reportData.groupByFunctionMap.lab_exam);
        if (reportData.summaryAdvice?.auditorSignPic) {
          reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
        }
        if (reportData.summaryAdvice?.preAuditorSignPic) {
          reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
        }
        if (reportData.summaryAdvice?.creatorSignPic) {
          reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
        }
        console.log('=======reportData==========', reportData);
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
        if (!reportRef.value) {
          message.error('报告组件初始化失败！');
          return;
        }
        reportRef.value.open({
          filename: `${currentReg.value.name}的体检报告`,
          template: template,
        });
      } else {
        message.error('获取报告数据失败');
      }
    } catch (error) {
      console.error(error);
    } finally {
      reportDataLoading.value = false;
    }
  }

  async function printReport() {
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    if (customerSummary.value.status != '审核通过') {
      message.warn('总检未审核，不可以打印报告！');
      return;
    }

    previewReport();

    await updateReportPrintTimes({ id: customerSummary.value.id });
  }

  function fetchReportTemplateList() {
    getReportTemplate({ type: '报告' }).then((res) => {
      if (res.success) {
        originReportTemplateList.value = res.result;
        reportTemplateList.value = res.result.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  }

  function filterTemplateByReg() {
    let reg = currentReg.value;
    if (!reg.id) {
      return;
    }
    let examCategory = reg.examCategory;
    let regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == examCategory);
    currentReportTemplateId.value = regTemplateList[0]?.id;
  }

  function handleHistoryResultReportId() {
    querySysParamByCode({ code: 'historyResultReportId' }).then((res) => {
      historyResultReportId.value = res.result;
    });
  }

  // Update onMounted to include cleanup on component unmount
  onMounted(() => {
    aiSummary.value = getSettingFromLocalStorage('aiSummary', true);
    abnormalDepartSummary.value = getSettingFromLocalStorage('abnormalDepartSummary', true);
    handleHistoryResultReportId();
    fetchReportTemplateList();
    fetchUrlSetting();
  });

  // Add beforeUnmount hook
  onBeforeUnmount(() => {});
</script>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }

  .chosenClass {
    opacity: 1;
    border-style: solid;
    border-width: 1px;
    border-color: v-bind('token.colorPrimary');
  }

  .ghost {
    border: solid 1px v-bind('token.colorPrimary') !important;
  }

  .drag-handle {
    cursor: move; /* Change the cursor to a 'move' icon when hovering over the handle */
  }

  .current-advice {
    border: 1px solid v-bind('token.colorPrimary');
    border-radius: 2px;
  }

  :deep(.ant-card-body) {
    padding: 6px;
  }

  :deep(.ant-collapse-header) {
    padding: 2px 8px;
  }

  .fade-move,
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: scaleY(0.01) translate(30px, 0);
  }

  .fade-leave-active {
    position: absolute;
  }

  .highlight {
    background-color: #f0f0f0;
    transition: background-color 0.3s ease;
  }

  .hint-bar {
    position: fixed;
    top: 95%;
    left: 0;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 0 5px 5px 0;
    z-index: 1000;
  }

  .chosen {
    background-color: #e0e0e0;
  }

  .drag {
    background-color: #c0c0c0;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
    width: 14px;
    font-size: 12px;
    letter-spacing: normal;
    line-height: 1;
    padding: 3px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .left-pane {
    background-color: #f0f0f0;
    padding: 20px;
  }

  .vertical-tab-text {
    /* 覆盖Ant Design Vue的默认样式 */

    .ant-tabs-nav {
      width: 18px !important;
      min-width: 18px !important;
    }

    .ant-tabs-tab {
      padding: 2px 0 !important;
      margin: 0 0 2px 0 !important;
      height: auto !important;
      min-height: 40px !important;
    }

    .ant-tabs-tab-btn {
      padding: 0 !important;
    }

    .ant-tabs-content-holder {
      border-left: none !important;
    }

    .ant-tabs-ink-bar {
      width: 1px !important;
    }

    .ant-tabs-tab-active {
      background-color: #f0f0f0 !important;
    }

    .ant-tabs-tabpane {
      padding-right: 2px !important;
    }

    /* 右侧标签页特定样式 */

    &.ant-tabs-right {
      .ant-tabs-nav {
        width: 18px !important;
      }

      .ant-tabs-nav-list {
        width: 18px !important;
      }

      .ant-tabs-tab {
        width: 18px !important;
        justify-content: center !important;
        text-align: center !important;
      }
    }
  }

  .right-pane {
    background-color: #e0e0e0;
    padding: 20px;
  }

  /* 确保a-tabs内容区域充分利用空间 */
  .ant-tabs-content {
    height: 100%;
  }

  .ant-tabs-tabpane {
    height: 100%;
    overflow: hidden;
  }

  /* 体检信息单行布局 */
  .exam-info-single-row {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 6px 8px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 6px;
    font-size: 13px;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    height: 36px;
  }

  .info-label {
    color: rgba(0, 0, 0, 0.65);
    margin-right: 2px;
    white-space: nowrap;
    font-size: 13px;
  }

  .info-value {
    color: rgba(0, 0, 0, 0.85);
    margin-right: 4px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 150px;
    font-size: 13px;
  }

  .info-value.strong {
    font-weight: bold;
  }

  .exam-no {
    margin-left: 4px;
    margin-right: 4px;
  }

  .info-divider {
    color: #d9d9d9;
    margin: 0 4px;
  }

  /* 标签样式调整 */
  .exam-info-single-row .ant-tag {
    margin-right: 4px;
    line-height: 18px;
    height: 20px;
    padding: 0 5px;
    font-size: 12px;
  }

  .bottom-bar {
    width: 100%;
    padding: 10px;
    background-color: #ffffff;
  }

  /* 优化底部操作栏样式 */
  .bottom-action-bar {
    position: fixed;
    bottom: 0;
    right: 40px; /* 考虑右侧标签栏宽度 */
    width: calc(75% - 45px); /* 减去右侧标签栏宽度和边距 */
    background-color: #fff;
    padding: 10px 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
    z-index: 100;
    border-top: 1px solid #f0f0f0;
    border-radius: 4px 4px 0 0;
    transition: all 0.3s;
  }

  .action-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .left-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 8px; /* 按钮之间的间距 */
  }

  /* 为按钮添加悬停效果 */
  .right-actions .ant-btn:hover {
    transform: translateY(-2px);
    transition: transform 0.2s;
  }

  /* 返回列表图标按钮样式 */
  .return-list-icon {
    margin-left: auto;
    padding: 0 8px;
    height: 28px;
    display: flex;
    align-items: center;
    font-size: 13px;
  }

  .return-list-icon .anticon {
    margin-right: 4px;
  }
</style>
