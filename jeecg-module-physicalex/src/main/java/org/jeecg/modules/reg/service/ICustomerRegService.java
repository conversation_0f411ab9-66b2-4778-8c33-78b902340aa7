package org.jeecg.modules.reg.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.excommons.BatchResult;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import org.jeecg.modules.fee.bo.PaymentAnalysis;
import org.jeecg.modules.reg.dto.AddItemGroupWithCheckPartsRequest;
import org.jeecg.modules.reg.dto.DependentItemResultDTO;
import org.jeecg.modules.reg.bo.RegTemplate;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.reg.bo.GuidanceSheet;
import org.jeecg.modules.reg.bo.StatBean;
import org.jeecg.modules.reg.entity.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface ICustomerRegService extends IService<CustomerReg> {

    Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, String idCard, String examNo, String dateType, String regTimeStart, String regTimeEnd, String companyRegId, String teamId, String name, String status, String retreiveStatus, String emplyee, String paymentState, String companyNotifyFlag,String customerId,String examCategory);

    Page<CustomerReg> pageCustomerReg4Occu(Page<CustomerReg> page, String idCard, String examNo, String dateType, String regTimeStart, String regTimeEnd, String companyRegId, String teamId, String name, String status, String retreiveStatus, String emplyee, String paymentState, String companyNotifyFlag,String customerId,String riskFactor,String examCategory,String occuReportResultStatus, String occuReportUploadTimeStart, String occuReportUploadTimeEnd,String summaryStatus);

    List<CustomerReg> listCustomerReg(String idCard, String examNo, String regTimeStart, String regTimeEnd, String companyRegId, String teamId, String name, String status, String retreiveStatus, String emplyee, String paymentState);

    Map<String, StatBean> statReg();

    String getDictText4ExcelTemplate(String companyRegId);

    CustomerReg getCustomerRegDetail(String id);

    Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws Exception;

    void fillNames(CustomerReg customerReg);

    void setExamNo(CustomerReg customerReg);

    List<CustomerRegItemGroup> getItemGroupByCustomerRegId(String customerRegId);

    /**
     * 新增：获取带完整依赖关系分析的项目列表
     */
    CustomerRegItemGroupAnalysisVO getItemGroupWithDependencyAnalysis(String customerRegId);

    /**
     * 新增：智能添加项目组合（自动处理子项目）
     */
    void addItemGroupWithAutoSubItems(List<CustomerRegItemGroup> groupList) throws Exception;

    /**
     * 新增：智能添加项目组合（自动处理子项目，支持跳过某些类型）
     */
    void addItemGroupWithAutoSubItems(List<CustomerRegItemGroup> groupList, boolean skipGiftAndAttach) throws Exception;

    void addItemGroupBatch(List<CustomerRegItemGroup> customerRegItemGroupList);

    BatchResult<CustomerReg> regBatchByIds(List<String> idList, String clientIp) throws Exception;

    BatchResult<CustomerReg> regBatch(List<CustomerReg> regList, String clientIp) throws Exception;

    PaymentAnalysis reg(CustomerReg customerReg, String clientIp) throws Exception;

    PaymentAnalysis regById(String id, String clientIp) throws Exception;

    void doSendReportNotifiyTask() throws Exception;

    List<CustomerRegItemGroup> getItemGroupByTeamId(String teamId, CustomerReg customerReg);

    void updateItemGroupBatch(List<CustomerRegItemGroup> customerRegItemGroupList);

    List<String> removeItemGroupBatch(String customerRegId, List<String> idList) throws Exception;

    List<String> minusItemGroupBatch(String customerRegId, List<String> idList) throws Exception;

    List<String> undoMinusItemGroupBatch(String customerRegId, List<String> idList) throws Exception;

    void setItemGroupBySuit(String customerRegId, List<CustomerRegItemGroup> groupList) throws Exception;

    void addItemGroup(List<CustomerRegItemGroup> groupList) throws Exception;

    /**
     * 添加项目组合（支持跳过赠送和附属项目处理）
     * @param groupList 项目组合列表
     * @param skipGiftAndAttach 是否跳过赠送和附属项目处理（true=跳过，false=处理）
     * @throws Exception
     */
    void addItemGroup(List<CustomerRegItemGroup> groupList, boolean skipGiftAndAttach) throws Exception;

    void changeItemGroupByCompanyTeam(String customerRegId, String companyTeamId) throws Exception;

    void addItemGroup4SmallApp(List<CustomerRegItemGroup> groupList) throws Exception;

    void updateGuidancePrintTimes(String id);

    GuidanceSheet getGuidanceSheet(String customerRegId);

    Map<String, List<CustomerRegItemGroup>> getElecGuidanceSheet(String customerRegId);

    List<CustomerRegItemGroup> list4GuidanceSheet(String customerRegId);

    void retrieve(String id, String retrieveImg);

    void unRetrieve(String id);

    void sendCustomerReg2Mq(CustomerReg reg);

    void sendCustomerRegUpdate2Mq(CustomerReg customerReg);

    CustomerReg addCustomerReg(CustomerReg customerReg);

    CustomerReg updateCustomerReg(CustomerReg customerReg);

    CustomerReg addCustomerReg4Order(CustomerOrder customerOrder) throws Exception;

    CustomerReg getByExamNo(String examNo);

    String statPayStatus4Reg(String regId);

    CustomerReg addCustomerReg2Interface(CustomerReg customerReg) throws Exception;

    public CompanyReg getCompanyItemGroups(String idCard) throws Exception;

    List<CustomerReg> listByIds(List<String> idList);

    void verifyAndSaveReport(String customerRegId, String reportUrl);

    void updateReportPrintStatus(String customerRegId);

    void markReportAsTaken(String customerRegId, String signFileUrl);

    List<CustomerReg> listByEReportStatus(String eReportStatus, String paperReportStatus, Integer limit);

    CustomerReg getLiteById(String id);

    CompanyTeam getTeamByIdcrad(String idCard) throws Exception;

    void updateHealthQuestId(String id, String healthQuestId);

    void fillPccaNameIfNull(CustomerReg customerReg);

    void verifyBatch(List<RegTemplate> idList) throws Exception;

    void resetState4ReGeneratePdf(List<String> idList) throws Exception;

    void markTakenBatch(List<String> idList, String signBase64) throws Exception;

    void assignSummaryDoctorBatch(List<String> idList, String summaryDoctorId, String summaryDoctorName) throws Exception;

    PaymentAnalysis handleAddItem(String id, String clientIp) throws Exception;

    List<CustomerReg> getLastNYearsReg(String regId, int years);

    List<CustomerReg> getLastNYearsRegByIdcard(String customerId, int years);

    List<CustomerReg> getLastNYearsRegByCustomer(String customerId, String status, int years);

    List<CustomerReg> getLastNYearsLiteRegByCustomer(String customerId, String status, int years);

    CustomerReg getRegWithItemGroup(String regId);

    void fixLimitAmount();

    void companyNotify(CustomerReg reg) throws Exception;

    Map<String, Integer> statByCustomer(String customerId, int years);

    void sendPsyNotify(String customerRegId) throws Exception;

    void sendReportNotify(String customerRegId) throws Exception;

    void reGeneratePdf(String customerRegId) throws Exception;

    void updateCustomerReportTemplate(String customerRegId, String templateId) throws Exception;

    List<CustomerReg> getRegListByIdCardOrExamNo(String idCard, String examNo);

    List<CustomerReg> getRegList4ReportByIdCardOrExamNo(String idCard, String examNo);

    void updateReportPrintTimesByRegId(String id);

    List<CustomerReg> getRegList4GroupByIdCardOrExamNo(String idCard, String examNo);

    List<CustomerRegItemGroup> addItemGroup4Machine(CustomerReg customerReg,List<ItemGroup> groupList,ItemSuit itemSuit) throws Exception;


    CompanyTeam getTeamAndLimitInfoByIdCard(String idCard) throws Exception;

    /**
     * 查询单个项目的依赖项目结果
     * @param customerRegId 体检登记ID
     * @param groupId 项目组ID
     * @return 依赖项目结果列表
     */
    List<DependentItemResultDTO> getDependentItemResults(String customerRegId, String groupId);

    /**
     * 批量查询多个项目的依赖项目结果
     * @param customerRegId 体检登记ID
     * @param groupIds 项目组ID列表
     * @return 依赖项目结果映射
     */
    Map<String, List<DependentItemResultDTO>> getDependentItemResultsBatch(String customerRegId, List<String> groupIds);

    /**
     * 查询体检人员的所有依赖项目结果
     * @param customerRegId 体检登记ID
     * @return 所有依赖项目结果映射
     */
    Map<String, List<DependentItemResultDTO>> getAllDependentItemResults(String customerRegId);

    /**
     * 添加带检查部位的项目组合
     * @param request 请求参数
     */
    void addItemGroupWithCheckParts(AddItemGroupWithCheckPartsRequest request);

    /**
     * 清除危害因素缓存
     * 当危害因素基础数据发生变更时调用此方法
     */
    void clearRiskFactorCache();
}
